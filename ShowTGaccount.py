#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TG爬虫账号数据统计脚本 - account_stats_enhanced.py
增加详细键值对输出功能
"""

import json
import redis
import time
from datetime import datetime
from collections import defaultdict
# from utils.config import redis_host, redis_port, redis_password

class TGAccountStatsEnhanced:
    def __init__(self):
        """初始化Redis连接"""
        redis_host="r-rj9ujtinnlom3flz8e.redis.rds.aliyuncs.com"
        try:
            # 使用项目配置的Redis连接
            print("开始连接：。。。。。。")
            redis_pool = redis.ConnectionPool.from_url(
                f"redis://{redis_host}", 
                port=16379, 
                username="r-rj9ujtinnlom3flz8e",
                password="Andata202308##", 
                encoding='utf-8', 
                decode_responses=True, 
                health_check_interval=60
            )
            self.redis_client = redis.Redis(connection_pool=redis_pool)
            
            # 测试连接
            self.redis_client.ping()
            print("✅ Redis连接成功")
            
        except Exception as e:
            print(f"❌ Redis连接失败: {e}")
            exit(1)
        
        # 基于项目代码的Redis键名
        self.account_keys = {
            # 基础账号池
            "库存池": "tg_spider:temp_banned",
            
            # IP绑定账号（消息采集）
            "消息采集账号": "tg_spider:account:*",
            
            # 监控账号
            "群组监控账号": "tg_spider:monitor_account:*", 
            
            # 专用功能账号
            "手机号搜索账号": "tg_spider:phone_search:account",
            "坐标搜索账号": "tg_spider:geo_accounts",
            "推广账号": "tg_spider:promotion:account",
            "群组信息采集账号": "tg_spider:group_info_account:*",
            
            # 状态管理
            "被封禁账号": "tg_spider:account_banned",
            "监控被封账号": "tg_spider:monitor_banned",
            "特殊推广账号": "tg_spider:promotion:special_phone",
        }
        
        # 统计相关键
        self.stats_keys = {
            "使用次数": "tg_spider:crawl:crawl_count:*",
            "手机号搜索次数": "tg_spider:phone_search_requests_num:*",
            "坐标搜索次数": "tg_spider:geo_search_requests_num:*",
            "群组信息请求次数": "tg_spider:group_info_requests_num:*",
        }

    def get_machine_ips(self):
        """获取所有机器IP"""
        ips = set()
        
        # 从消息采集账号键中提取IP
        account_keys = self.redis_client.keys("tg_spider:account:*")
        for key in account_keys:
            ip = key.split(":")[-1]
            if self._is_valid_ip(ip):
                ips.add(ip)
        
        # 从监控账号键中提取IP
        monitor_keys = self.redis_client.keys("tg_spider:monitor_account:*")
        for key in monitor_keys:
            ip = key.split(":")[-1]
            if self._is_valid_ip(ip):
                ips.add(ip)
        
        return sorted(list(ips))
    
    def _is_valid_ip(self, ip_str):
        """简单的IP格式验证"""
        parts = ip_str.split('.')
        if len(parts) != 4:
            return False
        try:
            return all(0 <= int(part) <= 255 for part in parts)
        except ValueError:
            return False

    def get_detailed_account_data(self, key_pattern, limit=None):
        """获取详细的账号键值对数据"""
        detailed_data = {}
        
        if "*" in key_pattern:
            keys = self.redis_client.keys(key_pattern)
        else:
            keys = [key_pattern] if self.redis_client.exists(key_pattern) else []
        
        for key in keys:
            try:
                key_type = self.redis_client.type(key)
                key_data = {
                    'type': key_type,
                    'ttl': self.redis_client.ttl(key),
                    'size': 0,
                    'data': None,
                    'sample_data': []
                }
                
                if key_type == 'set':
                    # Set类型数据
                    key_data['size'] = self.redis_client.scard(key)
                    
                    # 获取样本数据
                    sample_limit = limit if limit else min(10, key_data['size'])
                    if sample_limit > 0:
                        samples = list(self.redis_client.srandmember(key, sample_limit))
                        for sample in samples:
                            try:
                                # 尝试解析JSON
                                parsed_sample = json.loads(sample)
                                key_data['sample_data'].append({
                                    'raw': sample,
                                    'parsed': parsed_sample,
                                    'type': 'json'
                                })
                            except json.JSONDecodeError:
                                # 纯文本数据
                                key_data['sample_data'].append({
                                    'raw': sample,
                                    'parsed': sample,
                                    'type': 'text'
                                })
                
                elif key_type == 'string':
                    # String类型数据
                    raw_data = self.redis_client.get(key)
                    key_data['size'] = len(raw_data) if raw_data else 0
                    
                    if raw_data:
                        try:
                            # 尝试解析JSON
                            parsed_data = json.loads(raw_data)
                            key_data['data'] = {
                                'raw': raw_data,
                                'parsed': parsed_data,
                                'type': 'json'
                            }
                            
                            # 如果是数组，提取样本
                            if isinstance(parsed_data, list):
                                sample_limit = limit if limit else min(5, len(parsed_data))
                                for item in parsed_data[:sample_limit]:
                                    key_data['sample_data'].append({
                                        'raw': json.dumps(item, ensure_ascii=False),
                                        'parsed': item,
                                        'type': 'json'
                                    })
                        except json.JSONDecodeError:
                            # 纯文本数据
                            key_data['data'] = {
                                'raw': raw_data,
                                'parsed': raw_data,
                                'type': 'text'
                            }
                
                elif key_type == 'hash':
                    # Hash类型数据
                    hash_data = self.redis_client.hgetall(key)
                    key_data['size'] = len(hash_data)
                    key_data['data'] = {
                        'raw': hash_data,
                        'parsed': hash_data,
                        'type': 'hash'
                    }
                
                elif key_type == 'list':
                    # List类型数据
                    key_data['size'] = self.redis_client.llen(key)
                    sample_limit = limit if limit else min(5, key_data['size'])
                    if sample_limit > 0:
                        samples = self.redis_client.lrange(key, 0, sample_limit - 1)
                        for sample in samples:
                            try:
                                parsed_sample = json.loads(sample)
                                key_data['sample_data'].append({
                                    'raw': sample,
                                    'parsed': parsed_sample,
                                    'type': 'json'
                                })
                            except json.JSONDecodeError:
                                key_data['sample_data'].append({
                                    'raw': sample,
                                    'parsed': sample,
                                    'type': 'text'
                                })
                
                detailed_data[key] = key_data
                
            except Exception as e:
                detailed_data[key] = {
                    'error': str(e),
                    'type': 'error'
                }
        
        return detailed_data

    def print_detailed_account_data(self, category, key_pattern, limit=5):
        """打印详细的账号键值对数据"""
        print(f"\n🔍 {category} 详细数据")
        print("=" * 80)
        
        detailed_data = self.get_detailed_account_data(key_pattern, limit)
        
        if not detailed_data:
            print("   📭 无数据")
            return
        
        for key, data in detailed_data.items():
            if 'error' in data:
                print(f"\n❌ 键: {key}")
                print(f"   错误: {data['error']}")
                continue
            
            print(f"\n📋 键: {key}")
            print(f"   类型: {data['type']}")
            print(f"   大小: {data['size']}")
            print(f"   TTL: {data['ttl']} 秒" if data['ttl'] > 0 else "   TTL: 永久")
            
            # 显示数据内容
            if data['type'] == 'set':
                print(f"   📦 Set数据 (显示前{min(limit, len(data['sample_data']))}个):")
                for i, sample in enumerate(data['sample_data'], 1):
                    if sample['type'] == 'json':
                        print(f"      {i}. JSON数据:")
                        self._print_json_pretty(sample['parsed'], indent=10)
                    else:
                        print(f"      {i}. 文本: {sample['raw']}")
            
            elif data['type'] == 'string':
                if data['data']:
                    if data['data']['type'] == 'json':
                        parsed = data['data']['parsed']
                        if isinstance(parsed, list):
                            print(f"   📄 JSON数组 (共{len(parsed)}个元素, 显示前{len(data['sample_data'])}个):")
                            for i, sample in enumerate(data['sample_data'], 1):
                                print(f"      {i}. 元素:")
                                self._print_json_pretty(sample['parsed'], indent=10)
                        else:
                            print(f"   📄 JSON对象:")
                            self._print_json_pretty(parsed, indent=8)
                    else:
                        print(f"   📄 文本数据: {data['data']['raw'][:200]}...")
            
            elif data['type'] == 'hash':
                print(f"   🗂️  Hash数据:")
                hash_data = data['data']['parsed']
                for field, value in list(hash_data.items())[:limit]:
                    print(f"      {field}: {value}")
                if len(hash_data) > limit:
                    print(f"      ... 还有 {len(hash_data) - limit} 个字段")
            
            elif data['type'] == 'list':
                print(f"   📝 List数据 (显示前{len(data['sample_data'])}个):")
                for i, sample in enumerate(data['sample_data'], 1):
                    if sample['type'] == 'json':
                        print(f"      {i}. JSON数据:")
                        self._print_json_pretty(sample['parsed'], indent=10)
                    else:
                        print(f"      {i}. 文本: {sample['raw']}")

    def _print_json_pretty(self, data, indent=8):
        """美化打印JSON数据"""
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    print(f"{' ' * indent}{key}: {type(value).__name__}")
                else:
                    # 截断长字符串
                    if isinstance(value, str) and len(value) > 50:
                        display_value = value[:47] + "..."
                    else:
                        display_value = value
                    print(f"{' ' * indent}{key}: {display_value}")
        elif isinstance(data, list):
            print(f"{' ' * indent}[数组，{len(data)}个元素]")
            for i, item in enumerate(data[:3]):  # 只显示前3个元素
                if isinstance(item, dict):
                    print(f"{' ' * indent}  [{i}]: {type(item).__name__}")
                else:
                    print(f"{' ' * indent}  [{i}]: {item}")
            if len(data) > 3:
                print(f"{' ' * indent}  ... 还有 {len(data) - 3} 个元素")
        else:
            print(f"{' ' * indent}{data}")

    def print_all_detailed_data(self, limit=3):
        """打印所有账号类型的详细数据"""
        print("\n" + "=" * 80)
        print("🔍 详细键值对数据分析")
        print("=" * 80)
        
        for category, key_pattern in self.account_keys.items():
            self.print_detailed_account_data(category, key_pattern, limit)

    def print_specific_key_analysis(self, key_pattern, limit=10):
        """分析特定键的详细信息"""
        print(f"\n🔬 键模式分析: {key_pattern}")
        print("=" * 80)
        
        if "*" in key_pattern:
            keys = self.redis_client.keys(key_pattern)
            print(f"匹配到 {len(keys)} 个键:")
            
            for key in keys[:10]:  # 只显示前10个键
                print(f"\n📌 键: {key}")
                detailed_data = self.get_detailed_account_data(key, limit)
                
                if key in detailed_data:
                    data = detailed_data[key]
                    print(f"   类型: {data['type']}, 大小: {data['size']}")
                    
                    # 显示样本数据
                    if data['sample_data']:
                        print("   样本数据:")
                        for i, sample in enumerate(data['sample_data'][:3], 1):
                            if sample['type'] == 'json' and isinstance(sample['parsed'], dict):
                                phone = sample['parsed'].get('phone', '未知')
                                print(f"      {i}. 手机号: {phone}")
                            else:
                                print(f"      {i}. {sample['raw'][:50]}...")
            
            if len(keys) > 10:
                print(f"\n... 还有 {len(keys) - 10} 个键未显示")
        else:
            self.print_detailed_account_data("单键分析", key_pattern, limit)

    def export_detailed_data(self, filename=None, include_raw_data=False):
        """导出详细数据到JSON文件"""
        if not filename:
            filename = f"detailed_account_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "redis_server": f"{redis_host}:{redis_port}",
            "detailed_accounts": {}
        }
        
        for category, key_pattern in self.account_keys.items():
            print(f"正在导出 {category}...")
            detailed_data = self.get_detailed_account_data(key_pattern)
            
            # 清理数据以便JSON序列化
            cleaned_data = {}
            for key, data in detailed_data.items():
                if 'error' in data:
                    cleaned_data[key] = {"error": data['error']}
                    continue
                
                cleaned_item = {
                    "type": data['type'],
                    "size": data['size'],
                    "ttl": data['ttl']
                }
                
                if include_raw_data:
                    # 包含原始数据
                    if data['sample_data']:
                        cleaned_item['sample_data'] = []
                        for sample in data['sample_data']:
                            cleaned_item['sample_data'].append({
                                "type": sample['type'],
                                "parsed": sample['parsed']
                            })
                    
                    if data['data']:
                        cleaned_item['data'] = {
                            "type": data['data']['type'],
                            "parsed": data['data']['parsed']
                        }
                else:
                    # 只包含统计信息
                    if data['sample_data']:
                        cleaned_item['sample_count'] = len(data['sample_data'])
                
                cleaned_data[key] = cleaned_item
            
            export_data["detailed_accounts"][category] = cleaned_data
        
        # 保存到文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 详细数据已导出到: {filename}")

    def interactive_key_explorer(self):
        """交互式键探索器"""
        print("\n🔍 交互式Redis键探索器")
        print("输入 'quit' 退出，'help' 查看帮助")
        
        while True:
            try:
                user_input = input("\n请输入键模式 (支持通配符*): ").strip()
                
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'help':
                    print("帮助:")
                    print("  - 输入完整键名查看详细信息")
                    print("  - 使用 * 通配符匹配多个键")
                    print("  - 例如: tg_spider:account:*")
                    print("  - 例如: tg_spider:*phone*")
                    continue
                elif not user_input:
                    continue
                
                # 检查键是否存在
                if "*" in user_input:
                    keys = self.redis_client.keys(user_input)
                    if not keys:
                        print(f"❌ 未找到匹配的键: {user_input}")
                        continue
                    print(f"✅ 找到 {len(keys)} 个匹配的键")
                else:
                    if not self.redis_client.exists(user_input):
                        print(f"❌ 键不存在: {user_input}")
                        continue
                
                # 询问显示数量
                try:
                    limit_input = input("显示数量限制 (默认5): ").strip()
                    limit = int(limit_input) if limit_input else 5
                except ValueError:
                    limit = 5
                
                # 分析并显示
                self.print_specific_key_analysis(user_input, limit)
                
            except KeyboardInterrupt:
                print("\n\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")

    # 保留原有的所有方法...
    def get_account_count(self, key_pattern):
        """获取账号数量"""
        if "*" in key_pattern:
            keys = self.redis_client.keys(key_pattern)
            total_count = 0
            details = {}
            
            for key in keys:
                try:
                    if self.redis_client.type(key) == 'set':
                        count = self.redis_client.scard(key)
                    elif self.redis_client.type(key) == 'string':
                        data = self.redis_client.get(key)
                        if data:
                            accounts = json.loads(data)
                            count = len(accounts) if isinstance(accounts, list) else 1
                        else:
                            count = 0
                    else:
                        count = 0
                    
                    total_count += count
                    key_suffix = key.split(":")[-1]
                    details[key_suffix] = count
                    
                except Exception as e:
                    print(f"⚠️  处理键 {key} 时出错: {e}")
                    continue
            
            return total_count, details
        else:
            try:
                if self.redis_client.exists(key_pattern):
                    if self.redis_client.type(key_pattern) == 'set':
                        return self.redis_client.scard(key_pattern), {}
                    elif self.redis_client.type(key_pattern) == 'string':
                        data = self.redis_client.get(key_pattern)
                        if data:
                            accounts = json.loads(data)
                            return len(accounts) if isinstance(accounts, list) else 1, {}
                return 0, {}
            except Exception as e:
                print(f"⚠️  处理键 {key_pattern} 时出错: {e}")
                return 0, {}

    def print_header(self):
        """打印标题"""
        print("=" * 80)
        print(f"🔍 TG爬虫账号数据统计报告 (增强版)")
        print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 Redis服务器: {redis_host}:{redis_port}")
        print("=" * 80)

    def print_basic_stats(self):
        """打印基础统计信息"""
        print("\n📊 账号数量统计")
        print("-" * 60)
        
        total_accounts = 0
        
        for category, key_pattern in self.account_keys.items():
            count, details = self.get_account_count(key_pattern)
            total_accounts += count
            
            print(f"{category:15} : {count:6d} 个")
            
            # 显示详细分布（对于有*的键）
            if details and len(details) > 1:
                for key_suffix, sub_count in sorted(details.items()):
                    if sub_count > 0:
                        print(f"{'':17} └─ {key_suffix}: {sub_count} 个")
        
        print("-" * 60)
        print(f"{'总计':15} : {total_accounts:6d} 个")

    def run_enhanced_report(self, include_detailed=True, detail_limit=3):
        """运行增强版报告"""
        self.print_header()
        self.print_basic_stats()
        
        if include_detailed:
            self.print_all_detailed_data(detail_limit)

def main():
    """主函数"""
    try:
        stats = TGAccountStatsEnhanced()
        
        print("请选择功能:")
        print("1. 基础统计报告")
        print("2. 详细键值对报告")
        print("3. 交互式键探索器")
        print("4. 导出详细数据")
        print("5. 分析特定键模式")
        
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == "1":
            stats.run_enhanced_report(include_detailed=False)
        elif choice == "2":
            limit = input("每个键显示的数据条数 (默认3): ").strip()
            limit = int(limit) if limit.isdigit() else 3
            stats.run_enhanced_report(include_detailed=True, detail_limit=limit)
        elif choice == "3":
            stats.interactive_key_explorer()
        elif choice == "4":
            include_raw = input("是否包含原始数据? (y/N): ").strip().lower() == 'y'
            stats.export_detailed_data(include_raw_data=include_raw)
        elif choice == "5":
            key_pattern = input("请输入键模式 (如: tg_spider:account:*): ").strip()
            if key_pattern:
                limit = input("显示数量限制 (默认10): ").strip()
                limit = int(limit) if limit.isdigit() else 10
                stats.print_specific_key_analysis(key_pattern, limit)
        else:
            print("无效选择，运行基础报告")
            stats.run_enhanced_report(include_detailed=False)
            
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()