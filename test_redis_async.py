#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试异步Redis连接
"""

import asyncio
import redis.asyncio as redis

async def test_redis_connection():
    """测试Redis异步连接"""
    redis_host = "r-rj9ujtinnlom3flz8e.redis.rds.aliyuncs.com"
    redis_port = 16379
    redis_username = "r-rj9ujtinnlom3flz8e"
    redis_password = "Andata202308##"
    
    try:
        print("开始测试异步Redis连接...")
        
        # 创建Redis客户端
        client = redis.Redis(
            host=redis_host,
            port=redis_port,
            username=redis_username,
            password=redis_password,
            encoding='utf-8',
            decode_responses=True,
            socket_timeout=10,
            socket_connect_timeout=10,
            health_check_interval=60
        )
        
        # 测试连接
        print("正在ping Redis服务器...")
        result = await client.ping()
        print(f"✅ Redis连接成功! Ping结果: {result}")
        
        # 测试一些基本操作
        print("测试基本操作...")
        
        # 测试keys操作
        keys = await client.keys("tg_spider:*")
        print(f"找到 {len(keys)} 个匹配的键")
        
        if keys:
            # 测试获取第一个键的信息
            first_key = keys[0]
            key_type = await client.type(first_key)
            print(f"第一个键 '{first_key}' 的类型: {key_type}")
            
            if key_type == 'set':
                count = await client.scard(first_key)
                print(f"Set大小: {count}")
            elif key_type == 'string':
                value = await client.get(first_key)
                print(f"String值长度: {len(value) if value else 0}")
        
        # 关闭连接
        await client.aclose()
        print("✅ 测试完成，连接已关闭")
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_redis_connection())
